package handlers

import (
	"context"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/olzzhas/edunite-server/gateway/clients"
	pb "github.com/olzzhas/edunite-server/notification_service/proto"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type NotificationHandler struct {
	RabbitLogPublisher interface {
		PublishLog(level, message, service string, data map[string]any) error
	}
	NotificationClient *clients.NotificationClient
}

func NewNotificationHandler(notificationClient *clients.NotificationClient, logPublisher interface {
	PublishLog(level, message, service string, data map[string]any) error
}) *NotificationHandler {
	return &NotificationHandler{
		RabbitLogPublisher: logPublisher,
		NotificationClient: notificationClient,
	}
}

// CreateNotificationHandler создает новое уведомление
func (h *NotificationHandler) CreateNotificationHandler(c *gin.Context) {
	var req struct {
		Title         string  `json:"title" binding:"required"`
		Message       string  `json:"message" binding:"required"`
		Type          string  `json:"type"`
		Priority      string  `json:"priority"`
		TargetType    string  `json:"target_type" binding:"required"`
		TargetValue   string  `json:"target_value"`
		SendEmail     bool    `json:"send_email"`
		EmailSubject  string  `json:"email_subject"`
		EmailTemplate string  `json:"email_template"`
		ScheduledAt   *string `json:"scheduled_at"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get sender ID from context (set by auth middleware)
	senderID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Convert types
	notificationType := convertStringToNotificationType(req.Type)
	notificationPriority := convertStringToNotificationPriority(req.Priority)
	targetType := convertStringToTargetType(req.TargetType)

	// Create gRPC request
	grpcReq := &pb.CreateNotificationRequest{
		Title:      req.Title,
		Message:    req.Message,
		Type:       notificationType,
		Priority:   notificationPriority,
		TargetType: targetType,
		SendEmail:  req.SendEmail,
		SenderId:   senderID.(int64),
	}

	if req.TargetValue != "" {
		grpcReq.TargetValue = req.TargetValue
	}

	if req.EmailSubject != "" {
		grpcReq.EmailSubject = req.EmailSubject
	}

	if req.EmailTemplate != "" {
		grpcReq.EmailTemplate = req.EmailTemplate
	}

	if req.ScheduledAt != nil && *req.ScheduledAt != "" {
		scheduledTime, err := time.Parse(time.RFC3339, *req.ScheduledAt)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid scheduled_at format. Use RFC3339 format"})
			return
		}
		grpcReq.ScheduledAt = timestamppb.New(scheduledTime)
	}

	// Call notification service
	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()

	resp, err := h.NotificationClient.CreateNotification(ctx, grpcReq)
	if err != nil {
		h.RabbitLogPublisher.PublishLog("error", "Failed to create notification", "gateway", map[string]any{
			"error":  err.Error(),
			"sender": senderID,
		})
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create notification"})
		return
	}

	if !resp.Success {
		c.JSON(http.StatusBadRequest, gin.H{"error": resp.Message})
		return
	}

	h.RabbitLogPublisher.PublishLog("info", "Notification created successfully", "gateway", map[string]any{
		"notification_id": resp.Notification.Id,
		"sender":          senderID,
		"target_type":     req.TargetType,
	})

	c.JSON(http.StatusCreated, resp)
}

// GetUserNotificationsHandler получает уведомления пользователя
func (h *NotificationHandler) GetUserNotificationsHandler(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := strconv.ParseInt(userIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	// Check if user can access these notifications (either own notifications or admin)
	currentUserID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	userRole, _ := c.Get("user_role")
	if currentUserID.(int64) != userID && userRole != "admin" {
		c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
		return
	}

	// Parse query parameters
	page, _ := strconv.ParseInt(c.DefaultQuery("page", "1"), 10, 32)
	limit, _ := strconv.ParseInt(c.DefaultQuery("limit", "20"), 10, 32)
	unreadOnly := c.DefaultQuery("unread_only", "false") == "true"

	// Create gRPC request
	grpcReq := &pb.GetUserNotificationsRequest{
		UserId:     userID,
		Page:       int32(page),
		Limit:      int32(limit),
		UnreadOnly: unreadOnly,
	}

	// Call notification service
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	resp, err := h.NotificationClient.GetUserNotifications(ctx, grpcReq)
	if err != nil {
		h.RabbitLogPublisher.PublishLog("error", "Failed to get user notifications", "gateway", map[string]any{
			"error":   err.Error(),
			"user_id": userID,
		})
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get notifications"})
		return
	}

	c.JSON(http.StatusOK, resp)
}

// GetAllNotificationsHandler получает все уведомления (только для админов)
func (h *NotificationHandler) GetAllNotificationsHandler(c *gin.Context) {
	// Parse query parameters
	page, _ := strconv.ParseInt(c.DefaultQuery("page", "1"), 10, 32)
	limit, _ := strconv.ParseInt(c.DefaultQuery("limit", "20"), 10, 32)
	targetType := convertStringToTargetType(c.DefaultQuery("target_type", ""))
	targetValue := c.DefaultQuery("target_value", "")

	// Create gRPC request
	grpcReq := &pb.GetAllNotificationsRequest{
		Page:        int32(page),
		Limit:       int32(limit),
		TargetType:  targetType,
		TargetValue: targetValue,
	}

	// Call notification service
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	resp, err := h.NotificationClient.GetAllNotifications(ctx, grpcReq)
	if err != nil {
		h.RabbitLogPublisher.PublishLog("error", "Failed to get all notifications", "gateway", map[string]any{
			"error": err.Error(),
		})
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get notifications"})
		return
	}

	c.JSON(http.StatusOK, resp)
}

// MarkAsReadHandler отмечает уведомление как прочитанное
func (h *NotificationHandler) MarkAsReadHandler(c *gin.Context) {
	notificationIDStr := c.Param("notification_id")
	notificationID, err := strconv.ParseInt(notificationIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid notification ID"})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Create gRPC request
	grpcReq := &pb.MarkAsReadRequest{
		NotificationId: notificationID,
		UserId:         userID.(int64),
	}

	// Call notification service
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	resp, err := h.NotificationClient.MarkAsRead(ctx, grpcReq)
	if err != nil {
		h.RabbitLogPublisher.PublishLog("error", "Failed to mark notification as read", "gateway", map[string]any{
			"error":           err.Error(),
			"notification_id": notificationID,
			"user_id":         userID,
		})
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to mark notification as read"})
		return
	}

	if !resp.Success {
		c.JSON(http.StatusBadRequest, gin.H{"error": resp.Message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

// DeleteNotificationHandler удаляет уведомление (только для админов)
func (h *NotificationHandler) DeleteNotificationHandler(c *gin.Context) {
	notificationIDStr := c.Param("notification_id")
	notificationID, err := strconv.ParseInt(notificationIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid notification ID"})
		return
	}

	// Create gRPC request
	grpcReq := &pb.DeleteNotificationRequest{
		NotificationId: notificationID,
	}

	// Call notification service
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	resp, err := h.NotificationClient.DeleteNotification(ctx, grpcReq)
	if err != nil {
		h.RabbitLogPublisher.PublishLog("error", "Failed to delete notification", "gateway", map[string]any{
			"error":           err.Error(),
			"notification_id": notificationID,
		})
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete notification"})
		return
	}

	if !resp.Success {
		c.JSON(http.StatusBadRequest, gin.H{"error": resp.Message})
		return
	}

	h.RabbitLogPublisher.PublishLog("info", "Notification deleted successfully", "gateway", map[string]any{
		"notification_id": notificationID,
	})

	c.JSON(http.StatusOK, resp)
}

// GetNotificationStatsHandler получает статистику уведомлений пользователя
func (h *NotificationHandler) GetNotificationStatsHandler(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := strconv.ParseInt(userIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	// Check if user can access these stats (either own stats or admin)
	currentUserID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	userRole, _ := c.Get("user_role")
	if currentUserID.(int64) != userID && userRole != "admin" {
		c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
		return
	}

	// Create gRPC request
	grpcReq := &pb.GetNotificationStatsRequest{
		UserId: userID,
	}

	// Call notification service
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	resp, err := h.NotificationClient.GetNotificationStats(ctx, grpcReq)
	if err != nil {
		h.RabbitLogPublisher.PublishLog("error", "Failed to get notification stats", "gateway", map[string]any{
			"error":   err.Error(),
			"user_id": userID,
		})
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get notification stats"})
		return
	}

	c.JSON(http.StatusOK, resp)
}

// Helper functions for type conversion
func convertStringToNotificationType(typeStr string) pb.NotificationType {
	switch typeStr {
	case "info":
		return pb.NotificationType_INFO
	case "warning":
		return pb.NotificationType_WARNING
	case "success":
		return pb.NotificationType_SUCCESS
	case "error":
		return pb.NotificationType_ERROR
	case "announcement":
		return pb.NotificationType_ANNOUNCEMENT
	default:
		return pb.NotificationType_INFO
	}
}

func convertStringToNotificationPriority(priorityStr string) pb.NotificationPriority {
	switch priorityStr {
	case "low":
		return pb.NotificationPriority_LOW
	case "normal":
		return pb.NotificationPriority_NORMAL
	case "high":
		return pb.NotificationPriority_HIGH
	case "urgent":
		return pb.NotificationPriority_URGENT
	default:
		return pb.NotificationPriority_NORMAL
	}
}

func convertStringToTargetType(targetTypeStr string) pb.TargetType {
	switch targetTypeStr {
	case "all":
		return pb.TargetType_ALL
	case "role":
		return pb.TargetType_ROLE
	case "user":
		return pb.TargetType_USER
	case "degree":
		return pb.TargetType_DEGREE
	case "course":
		return pb.TargetType_COURSE
	case "thread":
		return pb.TargetType_THREAD
	default:
		return pb.TargetType_ALL
	}
}
