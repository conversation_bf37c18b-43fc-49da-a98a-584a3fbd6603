# Notification Service - Полная документация

## Обзор системы

Notification Service - это микросервис для управления уведомлениями в системе EduNite. Сервис поддерживает:
- Создание и отправку уведомлений
- Email уведомления с шаблонами
- Таргетированные уведомления (всем пользователям, по ролям, конкретным пользователям, степеням, курсам, потокам)
- Отложенные уведомления
- Статистику уведомлений
- CRUD операции для администраторов

## Архитектура

### Структура базы данных

#### Таблица `notifications`
```sql
- id (BIGSERIAL PRIMARY KEY)
- title (VARCHAR(255) NOT NULL) - заголовок уведомления
- message (TEXT NOT NULL) - текст сообщения
- type (notification_type) - тип: info, warning, success, error, announcement
- priority (notification_priority) - приоритет: low, normal, high, urgent
- target_type (target_type) - тип получателей: all, role, user, degree, course, thread
- target_value (VARCHAR(255)) - значение цели (имя роли, user_id, degree_id и т.д.)
- sender_id (BIGINT) - ID отправителя
- send_email (BOOLEAN) - отправлять ли email
- email_subject (VARCHAR(255)) - тема email
- email_template (TEXT) - шаблон email
- scheduled_at (TIMESTAMP) - время отложенной отправки
- sent_at (TIMESTAMP) - время фактической отправки
- created_at, updated_at (TIMESTAMP)
```

#### Таблица `notification_recipients`
```sql
- id (BIGSERIAL PRIMARY KEY)
- notification_id (BIGINT) - ссылка на уведомление
- user_id (BIGINT) - ID получателя
- is_read (BOOLEAN) - прочитано ли
- read_at (TIMESTAMP) - время прочтения
- email_sent (BOOLEAN) - отправлен ли email
- email_sent_at (TIMESTAMP) - время отправки email
- created_at, updated_at (TIMESTAMP)
```

#### Таблица `email_templates`
```sql
- id (BIGSERIAL PRIMARY KEY)
- name (VARCHAR(255)) - имя шаблона
- subject (VARCHAR(255)) - тема письма
- html_content (TEXT) - HTML содержимое
- text_content (TEXT) - текстовое содержимое
- variables (JSONB) - доступные переменные
- is_active (BOOLEAN) - активен ли шаблон
- created_at, updated_at (TIMESTAMP)
```

### Предустановленные email шаблоны

1. **default_notification** - стандартный шаблон
2. **announcement** - для объявлений
3. **urgent_notification** - для срочных уведомлений

## API Endpoints

### 1. Создание уведомления (Админы и Преподаватели)

**POST** `/notifications` (только админы)
**POST** `/teacher/notifications` (преподаватели и админы)

**Headers:**
```
Authorization: Bearer <token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "title": "Заголовок уведомления",
  "message": "Текст сообщения",
  "type": "info",
  "priority": "normal",
  "target_type": "all",
  "target_value": "",
  "send_email": true,
  "email_subject": "Тема письма",
  "email_template": "default_notification",
  "scheduled_at": "2024-01-15T10:00:00Z"
}
```

**Параметры:**
- `title` (обязательно) - заголовок уведомления
- `message` (обязательно) - текст сообщения
- `type` - тип уведомления: "info", "warning", "success", "error", "announcement"
- `priority` - приоритет: "low", "normal", "high", "urgent"
- `target_type` (обязательно) - тип получателей: "all", "role", "user", "degree", "course", "thread"
- `target_value` - значение цели (например, "student" для роли, "123" для user_id)
- `send_email` - отправлять ли email уведомление
- `email_subject` - тема email (если send_email = true)
- `email_template` - имя шаблона email
- `scheduled_at` - время отложенной отправки (RFC3339 формат)

**Response:**
```json
{
  "notification": {
    "id": 1,
    "title": "Заголовок уведомления",
    "message": "Текст сообщения",
    "type": 0,
    "priority": 1,
    "target_type": 0,
    "target_value": "",
    "sender_id": 123,
    "send_email": true,
    "email_subject": "Тема письма",
    "email_template": "default_notification",
    "scheduled_at": "2024-01-15T10:00:00Z",
    "sent_at": null,
    "created_at": "2024-01-15T09:00:00Z",
    "updated_at": "2024-01-15T09:00:00Z"
  },
  "message": "Notification created successfully",
  "success": true
}
```

### 2. Получение уведомлений пользователя

**GET** `/users/{user_id}/notifications`

**Headers:**
```
Authorization: Bearer <token>
```

**Query Parameters:**
- `page` - номер страницы (по умолчанию: 1)
- `limit` - количество на странице (по умолчанию: 20)
- `unread_only` - только непрочитанные (true/false, по умолчанию: false)

**Response:**
```json
{
  "notifications": [
    {
      "notification": {
        "id": 1,
        "title": "Заголовок уведомления",
        "message": "Текст сообщения",
        "type": 0,
        "priority": 1,
        "target_type": 0,
        "target_value": "",
        "sender_id": 123,
        "send_email": true,
        "created_at": "2024-01-15T09:00:00Z"
      },
      "recipient": {
        "id": 1,
        "notification_id": 1,
        "user_id": 456,
        "is_read": false,
        "read_at": null,
        "email_sent": true,
        "email_sent_at": "2024-01-15T09:01:00Z",
        "created_at": "2024-01-15T09:00:00Z"
      }
    }
  ],
  "total_count": 15,
  "unread_count": 3
}
```

### 3. Получение всех уведомлений (Админы)

**GET** `/notifications`

**Headers:**
```
Authorization: Bearer <token>
```

**Query Parameters:**
- `page` - номер страницы (по умолчанию: 1)
- `limit` - количество на странице (по умолчанию: 20)
- `target_type` - фильтр по типу получателей
- `target_value` - фильтр по значению цели

**Response:**
```json
{
  "notifications": [
    {
      "id": 1,
      "title": "Заголовок уведомления",
      "message": "Текст сообщения",
      "type": 0,
      "priority": 1,
      "target_type": 0,
      "target_value": "",
      "sender_id": 123,
      "send_email": true,
      "created_at": "2024-01-15T09:00:00Z"
    }
  ],
  "total_count": 50
}
```

### 4. Отметить уведомление как прочитанное

**PUT** `/notifications/{notification_id}/read`

**Headers:**
```
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "message": "Notification marked as read"
}
```

### 5. Удаление уведомления (Админы)

**DELETE** `/notifications/{notification_id}`

**Headers:**
```
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "message": "Notification deleted successfully"
}
```

### 6. Статистика уведомлений пользователя

**GET** `/users/{user_id}/notifications/stats`

**Headers:**
```
Authorization: Bearer <token>
```

**Response:**
```json
{
  "total_notifications": 25,
  "unread_notifications": 3,
  "read_notifications": 22,
  "email_notifications": 15
}
```

## Примеры использования

### Создание объявления для всех пользователей
```json
POST /notifications
{
  "title": "Важное объявление",
  "message": "Завтра в университете будет проводиться техническое обслуживание системы с 02:00 до 06:00",
  "type": "announcement",
  "priority": "high",
  "target_type": "all",
  "send_email": true,
  "email_subject": "Техническое обслуживание системы",
  "email_template": "announcement"
}
```

### Уведомление для конкретной роли
```json
POST /notifications
{
  "title": "Напоминание для студентов",
  "message": "Не забудьте сдать курсовые работы до конца недели",
  "type": "info",
  "priority": "normal",
  "target_type": "role",
  "target_value": "student",
  "send_email": false
}
```

### Срочное уведомление для конкретного пользователя
```json
POST /notifications
{
  "title": "Срочное уведомление",
  "message": "Ваша заявка требует немедленного рассмотрения",
  "type": "error",
  "priority": "urgent",
  "target_type": "user",
  "target_value": "123",
  "send_email": true,
  "email_template": "urgent_notification"
}
```

### Отложенное уведомление
```json
POST /notifications
{
  "title": "Напоминание о занятии",
  "message": "Завтра в 10:00 состоится лекция по математике",
  "type": "info",
  "priority": "normal",
  "target_type": "course",
  "target_value": "456",
  "send_email": true,
  "scheduled_at": "2024-01-16T08:00:00Z"
}
```

## Коды ошибок

- `400 Bad Request` - неверные параметры запроса
- `401 Unauthorized` - пользователь не аутентифицирован
- `403 Forbidden` - недостаточно прав доступа
- `404 Not Found` - уведомление не найдено
- `500 Internal Server Error` - внутренняя ошибка сервера

## Авторизация

- **Админы** - полный доступ ко всем операциям
- **Преподаватели** - могут создавать уведомления через `/teacher/notifications`
- **Студенты** - могут просматривать только свои уведомления и отмечать их как прочитанные

## Email шаблоны

Система поддерживает переменные в шаблонах:
- `{{title}}` - заголовок уведомления
- `{{message}}` - текст сообщения

Доступные шаблоны:
- `default_notification` - стандартный шаблон
- `announcement` - для объявлений с особым оформлением
- `urgent_notification` - для срочных уведомлений с красным оформлением
