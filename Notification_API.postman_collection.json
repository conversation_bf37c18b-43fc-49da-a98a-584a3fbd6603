{"info": {"_postman_id": "notification-api-collection", "name": "Notification API", "description": "API коллекция для работы с уведомлениями в системе EduNite", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Admin Operations", "item": [{"name": "Create Notification (Admin)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Важное объявление\",\n  \"message\": \"Завтра в университете будет проводиться техническое обслуживание системы с 02:00 до 06:00\",\n  \"type\": \"announcement\",\n  \"priority\": \"high\",\n  \"target_type\": \"all\",\n  \"send_email\": true,\n  \"email_subject\": \"Техническое обслуживание системы\",\n  \"email_template\": \"announcement\"\n}"}, "url": {"raw": "{{base_url}}/notifications", "host": ["{{base_url}}"], "path": ["notifications"]}, "description": "Создание нового уведомления (только для администраторов)"}}, {"name": "Get All Notifications (Admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/notifications?page=1&limit=20&target_type=all", "host": ["{{base_url}}"], "path": ["notifications"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "20"}, {"key": "target_type", "value": "all"}]}, "description": "Получение всех уведомлений (только для администраторов)"}}, {"name": "Delete Notification (Admin)", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/notifications/{{notification_id}}", "host": ["{{base_url}}"], "path": ["notifications", "{{notification_id}}"]}, "description": "Удаление уведомления (только для администраторов)"}}], "description": "Операции доступные только администраторам"}, {"name": "Teacher Operations", "item": [{"name": "Create Notification (Teacher)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Напоминание о занятии\",\n  \"message\": \"Завтра в 10:00 состоится лекция по математике в аудитории 205\",\n  \"type\": \"info\",\n  \"priority\": \"normal\",\n  \"target_type\": \"course\",\n  \"target_value\": \"123\",\n  \"send_email\": true,\n  \"email_subject\": \"Напоминание о лекции\",\n  \"email_template\": \"default_notification\"\n}"}, "url": {"raw": "{{base_url}}/teacher/notifications", "host": ["{{base_url}}"], "path": ["teacher", "notifications"]}, "description": "Создание уведомления преподавателем"}}], "description": "Операции доступные преподавателям"}, {"name": "User Operations", "item": [{"name": "Get User Notifications", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/users/{{user_id}}/notifications?page=1&limit=20&unread_only=false", "host": ["{{base_url}}"], "path": ["users", "{{user_id}}", "notifications"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "20"}, {"key": "unread_only", "value": "false"}]}, "description": "Получение уведомлений пользователя"}}, {"name": "Get User Notifications (Unread Only)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/users/{{user_id}}/notifications?unread_only=true", "host": ["{{base_url}}"], "path": ["users", "{{user_id}}", "notifications"], "query": [{"key": "unread_only", "value": "true"}]}, "description": "Получение только непрочитанных уведомлений пользователя"}}, {"name": "Mark Notification as Read", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/notifications/{{notification_id}}/read", "host": ["{{base_url}}"], "path": ["notifications", "{{notification_id}}", "read"]}, "description": "Отметить уведомление как прочитанное"}}, {"name": "Get User Notification Stats", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/users/{{user_id}}/notifications/stats", "host": ["{{base_url}}"], "path": ["users", "{{user_id}}", "notifications", "stats"]}, "description": "Получение статистики уведомлений пользователя"}}], "description": "Операции доступные всем пользователям"}, {"name": "Notification Examples", "item": [{"name": "Create Announcement for All Users", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Важное объявление для всех\",\n  \"message\": \"Уважаемые пользователи! Завтра с 02:00 до 06:00 будет проводиться плановое техническое обслуживание системы. В это время доступ к платформе будет ограничен.\",\n  \"type\": \"announcement\",\n  \"priority\": \"high\",\n  \"target_type\": \"all\",\n  \"send_email\": true,\n  \"email_subject\": \"Плановое техническое обслуживание\",\n  \"email_template\": \"announcement\"\n}"}, "url": {"raw": "{{base_url}}/notifications", "host": ["{{base_url}}"], "path": ["notifications"]}}}, {"name": "Create Notification for Students Only", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Напоминание для студентов\",\n  \"message\": \"Не забудьте сдать курсовые работы до конца недели. Последний срок сдачи - пятница, 18:00.\",\n  \"type\": \"info\",\n  \"priority\": \"normal\",\n  \"target_type\": \"role\",\n  \"target_value\": \"student\",\n  \"send_email\": false\n}"}, "url": {"raw": "{{base_url}}/notifications", "host": ["{{base_url}}"], "path": ["notifications"]}}}, {"name": "Create Urgent Notification for Specific User", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Срочное уведомление\",\n  \"message\": \"Ваша заявка на изменение расписания требует немедленного рассмотрения. Пожалуйста, свяжитесь с деканатом в ближайшее время.\",\n  \"type\": \"error\",\n  \"priority\": \"urgent\",\n  \"target_type\": \"user\",\n  \"target_value\": \"123\",\n  \"send_email\": true,\n  \"email_subject\": \"СРОЧНО: Требуется ваше внимание\",\n  \"email_template\": \"urgent_notification\"\n}"}, "url": {"raw": "{{base_url}}/notifications", "host": ["{{base_url}}"], "path": ["notifications"]}}}, {"name": "Create Scheduled Notification", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Напоминание о занятии\",\n  \"message\": \"Завтра в 10:00 состоится лекция по высшей математике в аудитории 205. Не забудьте взять с собой калькулятор.\",\n  \"type\": \"info\",\n  \"priority\": \"normal\",\n  \"target_type\": \"course\",\n  \"target_value\": \"456\",\n  \"send_email\": true,\n  \"email_subject\": \"Напоминание о лекции завтра\",\n  \"email_template\": \"default_notification\",\n  \"scheduled_at\": \"2024-01-16T08:00:00Z\"\n}"}, "url": {"raw": "{{base_url}}/notifications", "host": ["{{base_url}}"], "path": ["notifications"]}}}], "description": "Примеры различных типов уведомлений"}], "variable": [{"key": "base_url", "value": "http://localhost:8080", "type": "string"}, {"key": "auth_token", "value": "your_jwt_token_here", "type": "string"}, {"key": "user_id", "value": "123", "type": "string"}, {"key": "notification_id", "value": "1", "type": "string"}]}